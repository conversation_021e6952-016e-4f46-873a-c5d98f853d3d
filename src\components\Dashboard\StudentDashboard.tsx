import React, { useState } from 'react';
import { 
  BookOpen, 
  Clock, 
  Trophy, 
  TrendingUp, 
  Calendar,
  Target,
  Award,
  ChevronRight,
  Play,
  Star,
  ArrowRight
} from 'lucide-react';
import { Course, CourseEnrollment, UserProgress, ProgressStatus } from '../../types/index';
import { useCourse } from '../../contexts/CourseContext';
import { useLesson } from '../../contexts/LessonContext';
import { useAuth } from '../../contexts/AuthContext';
import ProgressDashboard from '../Progress/ProgressDashboard';
import { CircularProgress } from '../Progress/ProgressBar';

interface StudentDashboardProps {
  onCourseSelect?: (courseId: string) => void;
  onLessonSelect?: (lessonId: string) => void;
  className?: string;
}

const StudentDashboard: React.FC<StudentDashboardProps> = ({
  onCourseSelect,
  onLessonSelect,
  className = '',
}) => {
  const { user } = useAuth();
  const { state: courseState } = useCourse();
  const { state: lessonState } = useLesson();
  const [activeTab, setActiveTab] = useState<'overview' | 'progress' | 'activity'>('overview');

  // Calculate statistics
  const enrolledCourses = courseState.enrollments;
  const totalCourses = enrolledCourses.length;
  const completedCourses = enrolledCourses.filter(e => e.progress >= 100).length;
  const inProgressCourses = enrolledCourses.filter(e => e.progress > 0 && e.progress < 100).length;
  
  const totalTimeSpent = lessonState.progress.reduce((total, progress) => total + progress.timeSpent, 0);
  const totalLessonsCompleted = lessonState.progress.filter(p => p.status === ProgressStatus.COMPLETED).length;

  // Get recent activity
  const recentActivity = lessonState.progress
    .filter(p => p.lastAccessed)
    .sort((a, b) => (b.lastAccessed?.getTime() || 0) - (a.lastAccessed?.getTime() || 0))
    .slice(0, 5);

  // Get recommended next steps
  const getRecommendedNextSteps = () => {
    const recommendations: Array<{
      type: 'continue' | 'start' | 'review';
      courseId: string;
      lessonId?: string;
      title: string;
      description: string;
      progress?: number;
    }> = [];

    // Find lessons in progress
    const inProgressLessons = lessonState.progress.filter(p => p.status === ProgressStatus.IN_PROGRESS);
    inProgressLessons.forEach(progress => {
      const course = courseState.courses.find(c => 
        c.modules.some(m => m.lessons.some(l => l.id === progress.lessonId))
      );
      const lesson = course?.modules
        .flatMap(m => m.lessons)
        .find(l => l.id === progress.lessonId);
      
      if (course && lesson) {
        recommendations.push({
          type: 'continue',
          courseId: course.id,
          lessonId: lesson.id,
          title: `Continue: ${lesson.title}`,
          description: `Resume your progress in ${course.title}`,
          progress: 50 // Assuming 50% for in-progress
        });
      }
    });

    // Find next available lessons in enrolled courses
    enrolledCourses.forEach(enrollment => {
      const course = courseState.courses.find(c => c.id === enrollment.courseId);
      if (!course) return;

      // Find the next lesson to take
      for (const module of course.modules) {
        for (const lesson of module.lessons) {
          const lessonProgress = lessonState.progress.find(p => p.lessonId === lesson.id);
          
          // If lesson is not started and prerequisites are met
          if (!lessonProgress) {
            // Check prerequisites
            const prerequisitesMet = !lesson.prerequisites || lesson.prerequisites.every(prereqId => {
              const prereqProgress = lessonState.progress.find(p => p.lessonId === prereqId);
              return prereqProgress?.status === ProgressStatus.COMPLETED;
            });

            if (prerequisitesMet) {
              recommendations.push({
                type: 'start',
                courseId: course.id,
                lessonId: lesson.id,
                title: `Start: ${lesson.title}`,
                description: `Begin this lesson in ${course.title}`,
                progress: 0
              });
              return; // Only recommend one lesson per course
            }
          }
        }
      }
    });

    return recommendations.slice(0, 4); // Limit to 4 recommendations
  };

  const recommendations = getRecommendedNextSteps();

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  const formatLastActivity = (timestamp: number): string => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8 ${className}`}>
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white shadow-lg">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex-1">
            <h1 className="text-3xl font-bold mb-2">
              {getGreeting()}, {user?.name || 'Student'}!
            </h1>
            <p className="text-blue-100 text-lg">
              Ready to continue your learning journey?
            </p>
          </div>
          <div className="text-center sm:text-right">
            <div className="text-4xl font-bold mb-1">{totalLessonsCompleted}</div>
            <div className="text-sm text-blue-100 font-medium">Lessons Completed</div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-50 rounded-xl">
                <BookOpen className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{totalCourses}</p>
                <p className="text-sm text-gray-600 font-medium">Enrolled Courses</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-green-50 rounded-xl">
                <Trophy className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{completedCourses}</p>
                <p className="text-sm text-gray-600 font-medium">Completed</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-yellow-50 rounded-xl">
                <TrendingUp className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{inProgressCourses}</p>
                <p className="text-sm text-gray-600 font-medium">In Progress</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-purple-50 rounded-xl">
                <Clock className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{formatTime(totalTimeSpent)}</p>
                <p className="text-sm text-gray-600 font-medium">Time Spent</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-1 px-6" role="tablist">
            {[
              { id: 'overview', label: 'Overview', icon: Target },
              { id: 'progress', label: 'Progress', icon: TrendingUp },
              { id: 'activity', label: 'Activity', icon: Calendar }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center justify-center space-x-2 py-4 px-6 border-b-2 font-medium text-sm transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 bg-blue-50/50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                }`}
                role="tab"
                aria-selected={activeTab === tab.id}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-8">
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* Recommended Next Steps */}
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-6">Recommended Next Steps</h3>
                {recommendations.length === 0 ? (
                  <div className="text-center py-12 text-gray-500">
                    <Star className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                    <p className="text-lg font-medium">Great job! You're all caught up.</p>
                    <p className="text-sm mt-2">Consider enrolling in a new course to continue learning.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {recommendations.map((rec, index) => (
                      <div
                        key={index}
                        className="border border-gray-200 rounded-xl p-6 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer group"
                        onClick={() => {
                          if (rec.lessonId) {
                            onLessonSelect?.(rec.lessonId);
                          } else {
                            onCourseSelect?.(rec.courseId);
                          }
                        }}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-3">
                              {rec.type === 'continue' ? (
                                <Play className="w-5 h-5 text-blue-600" />
                              ) : rec.type === 'start' ? (
                                <Target className="w-5 h-5 text-green-600" />
                              ) : (
                                <Award className="w-5 h-5 text-yellow-600" />
                              )}
                              <span className={`text-xs font-semibold px-3 py-1 rounded-full ${
                                rec.type === 'continue' ? 'bg-blue-100 text-blue-800' :
                                rec.type === 'start' ? 'bg-green-100 text-green-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {rec.type === 'continue' ? 'Continue' : rec.type === 'start' ? 'Start' : 'Review'}
                              </span>
                            </div>
                            <h4 className="font-semibold text-gray-900 mb-2">{rec.title}</h4>
                            <p className="text-sm text-gray-600 leading-relaxed">{rec.description}</p>
                          </div>
                          <ChevronRight className="w-5 h-5 text-gray-400 ml-4 group-hover:text-gray-600 transition-colors" />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Current Courses */}
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-6">Your Courses</h3>
                <div className="space-y-6">
                  {enrolledCourses.slice(0, 3).map(enrollment => {
                    const course = courseState.courses.find(c => c.id === enrollment.courseId);
                    if (!course) return null;

                    const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);
                    const completedLessons = lessonState.progress.filter(p =>
                      course.modules.some(m => m.lessons.some(l => l.id === p.lessonId)) &&
                      p.status === ProgressStatus.COMPLETED
                    ).length;

                    return (
                      <div
                        key={course.id}
                        className="border border-gray-200 rounded-xl p-6 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer group"
                        onClick={() => onCourseSelect?.(course.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-gray-900 mb-2 text-lg">{course.title}</h4>
                            <p className="text-sm text-gray-600 mb-4 leading-relaxed">{course.description}</p>
                            <div className="flex items-center space-x-6 text-sm">
                              <span className="text-gray-600 font-medium">
                                <span className="text-gray-900">{completedLessons}</span>/{totalLessons} lessons
                              </span>
                              <span className="text-gray-600 font-medium">
                                <span className="text-gray-900">{Math.round(enrollment.progress)}%</span> complete
                              </span>
                            </div>
                          </div>
                          <div className="ml-8 flex-shrink-0">
                            <CircularProgress
                              progress={enrollment.progress}
                              size={64}
                              strokeWidth={6}
                              color={enrollment.progress >= 100 ? 'green' : enrollment.progress >= 50 ? 'blue' : 'yellow'}
                            />
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {enrolledCourses.length > 3 && (
                  <button
                    onClick={() => setActiveTab('progress')}
                    className="mt-6 flex items-center space-x-2 text-blue-600 hover:text-blue-800 text-sm font-semibold transition-colors"
                  >
                    <span>View all courses</span>
                    <ArrowRight className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          )}

          {activeTab === 'progress' && (
            <ProgressDashboard onCourseSelect={onCourseSelect} />
          )}

          {activeTab === 'activity' && (
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-6">Recent Activity</h3>
              {recentActivity.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <Calendar className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium">No recent activity found.</p>
                  <p className="text-sm mt-2">Start learning to see your activity here!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentActivity.map(progress => {
                    const course = courseState.courses.find(c =>
                      c.modules.some(m => m.lessons.some(l => l.id === progress.lessonId))
                    );
                    const lesson = course?.modules
                      .flatMap(m => m.lessons)
                      .find(l => l.id === progress.lessonId);

                    if (!course || !lesson) return null;

                    return (
                      <div
                        key={progress.id}
                        className="flex items-center justify-between p-6 bg-gray-50 rounded-xl hover:bg-gray-100 hover:shadow-sm cursor-pointer transition-all duration-200 group"
                        onClick={() => onLessonSelect?.(lesson.id)}
                      >
                        <div className="flex items-center space-x-4">
                          <div className={`p-3 rounded-xl ${
                            progress.status === ProgressStatus.COMPLETED
                              ? 'bg-green-100 text-green-600'
                              : 'bg-blue-100 text-blue-600'
                          }`}>
                            {progress.status === ProgressStatus.COMPLETED ? (
                              <Trophy className="w-5 h-5" />
                            ) : (
                              <Clock className="w-5 h-5" />
                            )}
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="font-semibold text-gray-900 mb-1">{lesson.title}</p>
                            <p className="text-sm text-gray-600">
                              {course.title} • {formatTime(progress.timeSpent)} spent
                            </p>
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0">
                          <p className="text-sm text-gray-500 mb-1">
                            {progress.lastAccessed ? formatLastActivity(progress.lastAccessed.getTime()) : 'Unknown'}
                          </p>
                          <p className={`text-sm font-semibold ${
                            progress.status === ProgressStatus.COMPLETED ? 'text-green-600' : 'text-blue-600'
                          }`}>
                            {progress.status === ProgressStatus.COMPLETED ? 'Completed' : 'In Progress'}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
