import React, { useState, useRef, useEffect } from 'react';
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Link,
  Image,
  Save,
} from 'lucide-react';
import { useDocument } from '../../contexts/DocumentContext';
import { Document } from '../../types/index';

interface RichTextEditorProps {
  document: Document;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({ document }) => {
  const { updateDocument } = useDocument();
  const [content, setContent] = useState(document.content);
  const [isEditing, setIsEditing] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setContent(document.content);
  }, [document.content]);

  const handleContentChange = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      setIsEditing(true);

      // Auto-save after 2 seconds of inactivity
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      
      saveTimeoutRef.current = setTimeout(() => {
        saveDocument(newContent);
      }, 2000);
    }
  };

  const saveDocument = async (contentToSave?: string) => {
    const finalContent = contentToSave || content;
    await updateDocument(document.id, { 
      content: finalContent,
      updatedAt: new Date(),
    });
    setIsEditing(false);
    setLastSaved(new Date());
  };

  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  const insertLink = () => {
    const url = prompt('Enter URL:');
    if (url) {
      execCommand('createLink', url);
    }
  };

  const insertImage = () => {
    const url = prompt('Enter image URL:');
    if (url) {
      execCommand('insertImage', url);
    }
  };

  const formatLastSaved = () => {
    if (!lastSaved) return '';
    return `Last saved at ${lastSaved.toLocaleTimeString()}`;
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Toolbar */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex flex-wrap items-center space-x-2">
          <div className="flex items-center space-x-1 border-r border-gray-200 pr-2">
            <button
              onClick={() => execCommand('bold')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Bold"
            >
              <Bold className="h-4 w-4" />
            </button>
            <button
              onClick={() => execCommand('italic')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Italic"
            >
              <Italic className="h-4 w-4" />
            </button>
            <button
              onClick={() => execCommand('underline')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Underline"
            >
              <Underline className="h-4 w-4" />
            </button>
          </div>

          <div className="flex items-center space-x-1 border-r border-gray-200 pr-2">
            <button
              onClick={() => execCommand('justifyLeft')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Align Left"
            >
              <AlignLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => execCommand('justifyCenter')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Align Center"
            >
              <AlignCenter className="h-4 w-4" />
            </button>
            <button
              onClick={() => execCommand('justifyRight')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Align Right"
            >
              <AlignRight className="h-4 w-4" />
            </button>
          </div>

          <div className="flex items-center space-x-1 border-r border-gray-200 pr-2">
            <button
              onClick={() => execCommand('insertUnorderedList')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Bullet List"
            >
              <List className="h-4 w-4" />
            </button>
            <button
              onClick={() => execCommand('insertOrderedList')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Numbered List"
            >
              <ListOrdered className="h-4 w-4" />
            </button>
            <button
              onClick={() => execCommand('formatBlock', 'blockquote')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Quote"
            >
              <Quote className="h-4 w-4" />
            </button>
          </div>

          <div className="flex items-center space-x-1 border-r border-gray-200 pr-2">
            <button
              onClick={() => execCommand('undo')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Undo"
            >
              <Undo className="h-4 w-4" />
            </button>
            <button
              onClick={() => execCommand('redo')}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Redo"
            >
              <Redo className="h-4 w-4" />
            </button>
          </div>

          <div className="flex items-center space-x-1 border-r border-gray-200 pr-2">
            <button
              onClick={insertLink}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Insert Link"
            >
              <Link className="h-4 w-4" />
            </button>
            <button
              onClick={insertImage}
              className="p-2 hover:bg-gray-100 rounded-md"
              title="Insert Image"
            >
              <Image className="h-4 w-4" />
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => saveDocument()}
              className="flex items-center px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              title="Save"
            >
              <Save className="h-4 w-4 mr-1" />
              Save
            </button>
            {isEditing && (
              <span className="text-sm text-gray-500">Unsaved changes</span>
            )}
            {lastSaved && (
              <span className="text-sm text-gray-400">{formatLastSaved()}</span>
            )}
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto p-8">
          <input
            type="text"
            value={document.title}
            onChange={(e) => updateDocument(document.id, { title: e.target.value })}
            className="w-full text-4xl font-bold text-gray-900 border-none outline-none mb-8 bg-transparent"
            placeholder="Document title..."
          />
          
          <div
            ref={editorRef}
            contentEditable
            onInput={handleContentChange}
            className="min-h-96 text-gray-800 leading-relaxed outline-none"
            style={{ fontSize: '16px', lineHeight: '1.6' }}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>
      </div>
    </div>
  );
};

export default RichTextEditor;