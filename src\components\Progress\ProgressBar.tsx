import React from 'react';
import { CheckCir<PERSON>, Clock, Lock } from 'lucide-react';

interface ProgressBarProps {
  progress: number; // 0-100
  total?: number;
  completed?: number;
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  showStats?: boolean;
  color?: 'blue' | 'green' | 'yellow' | 'red';
  animated?: boolean;
  className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  total,
  completed,
  label,
  size = 'md',
  showPercentage = true,
  showStats = false,
  color = 'blue',
  animated = true,
  className = '',
}) => {
  const clampedProgress = Math.min(Math.max(progress, 0), 100);
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500'
  };

  const textColorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    yellow: 'text-yellow-600',
    red: 'text-red-600'
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Header with label and percentage */}
      {(label || showPercentage) && (
        <div className="flex items-center justify-between mb-2">
          {label && (
            <span className="text-sm font-medium text-gray-700">{label}</span>
          )}
          {showPercentage && (
            <span className={`text-sm font-medium ${textColorClasses[color]}`}>
              {Math.round(clampedProgress)}%
            </span>
          )}
        </div>
      )}

      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full overflow-hidden">
        <div
          className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full transition-all duration-500 ease-out ${
            animated ? 'transform-gpu' : ''
          }`}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>

      {/* Stats */}
      {showStats && total !== undefined && completed !== undefined && (
        <div className="flex items-center justify-between mt-2 text-xs text-gray-600">
          <span>{completed} of {total} completed</span>
          <span>{total - completed} remaining</span>
        </div>
      )}
    </div>
  );
};

interface CircularProgressProps {
  progress: number; // 0-100
  size?: number;
  strokeWidth?: number;
  color?: 'blue' | 'green' | 'yellow' | 'red';
  showPercentage?: boolean;
  className?: string;
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  progress,
  size = 80,
  strokeWidth = 8,
  color = 'blue',
  showPercentage = true,
  className = '',
}) => {
  const clampedProgress = Math.min(Math.max(progress, 0), 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (clampedProgress / 100) * circumference;

  const colorClasses = {
    blue: 'stroke-blue-500',
    green: 'stroke-green-500',
    yellow: 'stroke-yellow-500',
    red: 'stroke-red-500'
  };

  const textColorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    yellow: 'text-yellow-600',
    red: 'text-red-600'
  };

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-gray-200"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={`${colorClasses[color]} transition-all duration-500 ease-out`}
        />
      </svg>
      {showPercentage && (
        <div className={`absolute inset-0 flex items-center justify-center text-sm font-semibold ${textColorClasses[color]}`}>
          {Math.round(clampedProgress)}%
        </div>
      )}
    </div>
  );
};

interface ProgressStepsProps {
  steps: Array<{
    id: string;
    title: string;
    status: 'completed' | 'current' | 'locked' | 'available';
    description?: string;
  }>;
  orientation?: 'horizontal' | 'vertical';
  showConnectors?: boolean;
  onStepClick?: (stepId: string) => void;
  className?: string;
}

export const ProgressSteps: React.FC<ProgressStepsProps> = ({
  steps,
  orientation = 'horizontal',
  showConnectors = true,
  onStepClick,
  className = '',
}) => {
  const getStepIcon = (status: string, index: number) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'current':
        return (
          <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full" />
          </div>
        );
      case 'locked':
        return <Lock className="w-5 h-5 text-gray-400" />;
      default:
        return (
          <div className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center text-xs font-medium text-gray-600">
            {index + 1}
          </div>
        );
    }
  };

  const getStepStyles = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 border-green-300 bg-green-50';
      case 'current':
        return 'text-blue-600 border-blue-300 bg-blue-50';
      case 'locked':
        return 'text-gray-400 border-gray-200 bg-gray-50';
      default:
        return 'text-gray-600 border-gray-300 bg-white hover:bg-gray-50';
    }
  };

  const isClickable = (status: string) => {
    return status !== 'locked' && onStepClick;
  };

  if (orientation === 'vertical') {
    return (
      <div className={`space-y-4 ${className}`}>
        {steps.map((step, index) => (
          <div key={step.id} className="relative">
            <div
              className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors ${
                getStepStyles(step.status)
              } ${isClickable(step.status) ? 'cursor-pointer' : ''}`}
              onClick={() => isClickable(step.status) && onStepClick?.(step.id)}
            >
              <div className="flex-shrink-0 mt-0.5">
                {getStepIcon(step.status, index)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium">{step.title}</p>
                {step.description && (
                  <p className="text-xs text-gray-500 mt-1">{step.description}</p>
                )}
              </div>
            </div>
            
            {/* Connector line */}
            {showConnectors && index < steps.length - 1 && (
              <div className="absolute left-6 top-12 w-0.5 h-4 bg-gray-200" />
            )}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`flex items-center ${className}`}>
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div
            className={`flex flex-col items-center space-y-2 p-2 rounded-lg transition-colors ${
              getStepStyles(step.status)
            } ${isClickable(step.status) ? 'cursor-pointer' : ''}`}
            onClick={() => isClickable(step.status) && onStepClick?.(step.id)}
          >
            {getStepIcon(step.status, index)}
            <div className="text-center">
              <p className="text-xs font-medium">{step.title}</p>
              {step.description && (
                <p className="text-xs text-gray-500 mt-1">{step.description}</p>
              )}
            </div>
          </div>
          
          {/* Connector line */}
          {showConnectors && index < steps.length - 1 && (
            <div className="flex-1 h-0.5 bg-gray-200 mx-2" />
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default ProgressBar;
