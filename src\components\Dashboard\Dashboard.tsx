import React from 'react';
import { useDocument } from '../../contexts/DocumentContext';
import { useAuth } from '../../contexts/AuthContext';
import { FileText, Plus, Users, Clock, TrendingUp } from 'lucide-react';

const Dashboard: React.FC = () => {
  const { state: documentState, createDocument, setActiveDocument } = useDocument();
  const { state: authState } = useAuth();

  const handleCreateDocument = async (type: 'article' | 'report' | 'proposal') => {
    const title = `New ${type.charAt(0).toUpperCase() + type.slice(1)}`;
    await createDocument(title, type);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getDocumentTypeColor = (type: string) => {
    switch (type) {
      case 'article':
        return 'bg-blue-100 text-blue-800';
      case 'report':
        return 'bg-green-100 text-green-800';
      case 'proposal':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex-1 overflow-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {authState.user?.name || 'User'}!
          </h1>
          <p className="mt-2 text-gray-600">
            Here's what's happening with your documents today.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Documents</p>
                <p className="text-2xl font-bold text-gray-900">{documentState.documents.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Collaborators</p>
                <p className="text-2xl font-bold text-gray-900">5</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Recent Activity</p>
                <p className="text-2xl font-bold text-gray-900">12</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">This Week</p>
                <p className="text-2xl font-bold text-gray-900">+3</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => handleCreateDocument('article')}
              className="p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow text-left"
            >
              <div className="flex items-center">
                <Plus className="h-8 w-8 text-blue-600 mr-4" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900">New Article</h3>
                  <p className="text-sm text-gray-500">Create a new article or blog post</p>
                </div>
              </div>
            </button>

            <button
              onClick={() => handleCreateDocument('report')}
              className="p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow text-left"
            >
              <div className="flex items-center">
                <Plus className="h-8 w-8 text-green-600 mr-4" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900">New Report</h3>
                  <p className="text-sm text-gray-500">Create a detailed report</p>
                </div>
              </div>
            </button>

            <button
              onClick={() => handleCreateDocument('proposal')}
              className="p-6 bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow text-left"
            >
              <div className="flex items-center">
                <Plus className="h-8 w-8 text-purple-600 mr-4" />
                <div>
                  <h3 className="text-lg font-medium text-gray-900">New Proposal</h3>
                  <p className="text-sm text-gray-500">Draft a new proposal</p>
                </div>
              </div>
            </button>
          </div>
        </div>

        {/* Recent Documents */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Documents</h2>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              {documentState.documents.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No documents yet. Create your first document!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {documentState.documents.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => setActiveDocument(doc)}
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <FileText className="h-6 w-6 text-gray-400" />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{doc.title}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDocumentTypeColor(doc.type)}`}>
                              {doc.type}
                            </span>
                            <span className="text-sm text-gray-500">
                              Updated {formatDate(doc.updatedAt)}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex -space-x-2">
                          {doc.collaborators.slice(0, 3).map((collaboratorId, index) => (
                            <div
                              key={collaboratorId}
                              className="h-6 w-6 bg-blue-500 rounded-full border-2 border-white flex items-center justify-center"
                            >
                              <span className="text-xs text-white font-medium">
                                {index + 1}
                              </span>
                            </div>
                          ))}
                        </div>
                        <span className="text-sm text-gray-500">
                          v{doc.version}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;