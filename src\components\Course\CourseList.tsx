import React, { useState, useEffect } from 'react';
import { Search, Filter, Grid, List } from 'lucide-react';
import { Course, CourseEnrollment, CourseFilters, CourseDifficulty } from '../../types/index';
import CourseCard from './CourseCard';
import { useCourse } from '../../contexts/CourseContext';
import { useAuth } from '../../contexts/AuthContext';

interface CourseListProps {
  title?: string;
  showFilters?: boolean;
  showEnrollButton?: boolean;
  onCourseSelect?: (courseId: string) => void;
  onEnroll?: (courseId: string) => void;
}

const CourseList: React.FC<CourseListProps> = ({
  title = 'Available Courses',
  showFilters = true,
  showEnrollButton = true,
  onCourseSelect,
  onEnroll,
}) => {
  const { state: courseState, loadCourses, enrollInCourse } = useCourse();
  const { state: authState } = useAuth();
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<CourseDifficulty | ''>('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);

  // Get all unique tags from courses
  const allTags = Array.from(
    new Set(courseState.courses.flatMap(course => course.tags))
  ).sort();

  useEffect(() => {
    const filters: CourseFilters = {};
    
    if (searchTerm) filters.search = searchTerm;
    if (selectedDifficulty) filters.difficulty = selectedDifficulty;
    if (selectedTags.length > 0) filters.tags = selectedTags;
    
    loadCourses(filters);
  }, [searchTerm, selectedDifficulty, selectedTags]);

  const handleEnroll = async (courseId: string) => {
    if (onEnroll) {
      onEnroll(courseId);
    } else {
      await enrollInCourse(courseId);
    }
  };

  const handleCourseView = (courseId: string) => {
    if (onCourseSelect) {
      onCourseSelect(courseId);
    }
  };

  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedDifficulty('');
    setSelectedTags([]);
  };

  const getEnrollmentForCourse = (courseId: string): CourseEnrollment | undefined => {
    return courseState.enrollments.find(e => e.courseId === courseId);
  };

  if (courseState.isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading courses...</span>
      </div>
    );
  }

  if (courseState.error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{courseState.error}</p>
        <button
          onClick={() => loadCourses()}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-md ${
              viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
            }`}
          >
            <Grid className="w-5 h-5" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-md ${
              viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
            }`}
          >
            <List className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      {showFilters && (
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search courses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
            </button>
            
            {(selectedDifficulty || selectedTags.length > 0) && (
              <button
                onClick={clearFilters}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Clear Filters
              </button>
            )}
          </div>

          {/* Filters Panel */}
          {showFiltersPanel && (
            <div className="bg-gray-50 p-4 rounded-md space-y-4">
              {/* Difficulty Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Difficulty Level
                </label>
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value as CourseDifficulty | '')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Levels</option>
                  <option value={CourseDifficulty.BEGINNER}>Beginner</option>
                  <option value={CourseDifficulty.INTERMEDIATE}>Intermediate</option>
                  <option value={CourseDifficulty.ADVANCED}>Advanced</option>
                </select>
              </div>

              {/* Tags Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Topics
                </label>
                <div className="flex flex-wrap gap-2">
                  {allTags.map(tag => (
                    <button
                      key={tag}
                      onClick={() => toggleTag(tag)}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        selectedTags.includes(tag)
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Results Count */}
      <div className="text-sm text-gray-600">
        {courseState.courses.length} course{courseState.courses.length !== 1 ? 's' : ''} found
      </div>

      {/* Course Grid/List */}
      {courseState.courses.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">No courses found matching your criteria.</p>
          <button
            onClick={clearFilters}
            className="text-blue-600 hover:text-blue-800"
          >
            Clear filters to see all courses
          </button>
        </div>
      ) : (
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {courseState.courses.map(course => (
            <CourseCard
              key={course.id}
              course={course}
              enrollment={getEnrollmentForCourse(course.id)}
              onEnroll={handleEnroll}
              onView={handleCourseView}
              showEnrollButton={showEnrollButton}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default CourseList;
