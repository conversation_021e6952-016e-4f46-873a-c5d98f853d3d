import React from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { useCourse } from '../../contexts/CourseContext';
import { useLesson } from '../../contexts/LessonContext';

interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

const Breadcrumbs: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const { state: courseState } = useCourse();
  const { state: lessonState } = useLesson();

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with Dashboard
    breadcrumbs.push({
      label: 'Dashboard',
      href: '/dashboard'
    });

    if (pathSegments.length === 0 || pathSegments[0] === 'dashboard') {
      breadcrumbs[0].current = true;
      return breadcrumbs;
    }

    // Handle different routes
    switch (pathSegments[0]) {
      case 'courses':
        breadcrumbs.push({
          label: 'Courses',
          href: '/courses'
        });

        if (params.courseId) {
          const course = courseState.courses.find(c => c.id === params.courseId);
          if (course) {
            breadcrumbs.push({
              label: course.title,
              href: `/courses/${course.id}`,
              current: pathSegments.length === 2
            });
          }
        } else {
          breadcrumbs[breadcrumbs.length - 1].current = true;
        }
        break;

      case 'lessons':
        if (params.lessonId) {
          // Find the lesson and its course
          let foundCourse = null;
          let foundModule = null;
          let foundLesson = null;

          for (const course of courseState.courses) {
            for (const module of course.modules) {
              const lesson = module.lessons.find(l => l.id === params.lessonId);
              if (lesson) {
                foundCourse = course;
                foundModule = module;
                foundLesson = lesson;
                break;
              }
            }
            if (foundLesson) break;
          }

          if (foundCourse && foundModule && foundLesson) {
            breadcrumbs.push({
              label: 'Courses',
              href: '/courses'
            });
            breadcrumbs.push({
              label: foundCourse.title,
              href: `/courses/${foundCourse.id}`
            });
            breadcrumbs.push({
              label: foundLesson.title,
              current: true
            });
          }
        }
        break;

      case 'admin':
        breadcrumbs.push({
          label: 'Admin Panel',
          current: true
        });
        break;

      case 'instructor':
        breadcrumbs.push({
          label: 'Instructor Panel',
          current: true
        });
        break;

      default:
        // For unknown routes, just capitalize the first segment
        breadcrumbs.push({
          label: pathSegments[0].charAt(0).toUpperCase() + pathSegments[0].slice(1),
          current: true
        });
        break;
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  const handleBreadcrumbClick = (href: string) => {
    navigate(href);
  };

  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 text-gray-400 mx-2" />
            )}
            
            {breadcrumb.current ? (
              <span className="text-sm font-medium text-gray-900">
                {breadcrumb.label}
              </span>
            ) : (
              <button
                onClick={() => breadcrumb.href && handleBreadcrumbClick(breadcrumb.href)}
                className="text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors"
              >
                {breadcrumb.label}
              </button>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
