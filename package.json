{"name": "collaborative-authoring-platform", "version": "0.0.0", "description": "A collaborative authoring platform built with React and TypeScript", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@types/react-router-dom": "^5.3.3", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.59.0", "react-router-dom": "^7.6.3", "socket.io-client": "^4.7.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}