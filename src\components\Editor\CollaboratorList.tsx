import React from 'react';
import { User, Crown, Eye, Edit3 } from 'lucide-react';
import { useDocument } from '../../contexts/DocumentContext';

const CollaboratorList: React.FC = () => {
  const { state } = useDocument();

  // Mock collaborators for demo
  const collaborators = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'owner' as const,
      avatar: null,
      isOnline: true,
      lastSeen: new Date(),
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'editor' as const,
      avatar: null,
      isOnline: true,
      lastSeen: new Date(),
    },
    {
      id: '3',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'viewer' as const,
      avatar: null,
      isOnline: false,
      lastSeen: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    },
  ];

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'editor':
        return <Edit3 className="h-4 w-4 text-blue-500" />;
      case 'viewer':
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (isOnline: boolean) => {
    return isOnline ? 'bg-green-400' : 'bg-gray-400';
  };

  const formatLastSeen = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <div className="w-64 bg-gray-50 border-l border-gray-200 p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Collaborators</h3>
      
      <div className="space-y-3">
        {collaborators.map((collaborator) => (
          <div
            key={collaborator.id}
            className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100"
          >
            <div className="relative">
              <div className="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
              <div
                className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white ${getStatusColor(
                  collaborator.isOnline
                )}`}
              />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {collaborator.name}
                </p>
                {getRoleIcon(collaborator.role)}
              </div>
              <p className="text-xs text-gray-500 truncate">
                {collaborator.isOnline ? 'Online' : formatLastSeen(collaborator.lastSeen)}
              </p>
            </div>
          </div>
        ))}
      </div>

      <button className="w-full mt-4 px-4 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50">
        Invite collaborators
      </button>
    </div>
  );
};

export default CollaboratorList;