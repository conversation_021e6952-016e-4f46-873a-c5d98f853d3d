import React, { useState, useEffect } from 'react';
import {
  Save,
  Plus,
  Trash2,
  Upload,
  X,
  FileText,
  Video,
  HelpCircle,
  Target,
  BookOpen,
  Link,
  Image,
  File,
  Clock,
  Eye,
  Settings
} from 'lucide-react';
import { Lesson, LessonType, QuestionType, InteractionType } from '../../types/index';

interface LessonEditorProps {
  lesson: Lesson;
  onSave: (lesson: Lesson) => void;
  onCancel: () => void;
  className?: string;
}

const LessonEditor: React.FC<LessonEditorProps> = ({
  lesson,
  onSave,
  onCancel,
  className = '',
}) => {
  const [editedLesson, setEditedLesson] = useState<Lesson>(lesson);
  const [activeTab, setActiveTab] = useState<'content' | 'settings' | 'preview'>('content');
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    setEditedLesson(lesson);
  }, [lesson]);

  const validateLesson = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!editedLesson.title.trim()) {
      newErrors.title = 'Lesson title is required';
    }

    // Type-specific validation
    switch (editedLesson.type) {
      case LessonType.TEXT:
        if (!editedLesson.content.text?.trim()) {
          newErrors.content = 'Text content is required';
        }
        break;
      case LessonType.VIDEO:
        if (!editedLesson.content.videoUrl?.trim()) {
          newErrors.videoUrl = 'Video URL is required';
        }
        break;
      case LessonType.QUIZ:
        if (!editedLesson.content.questions || editedLesson.content.questions.length === 0) {
          newErrors.questions = 'At least one question is required';
        }
        break;
      case LessonType.ASSIGNMENT:
        if (!editedLesson.content.instructions?.trim()) {
          newErrors.instructions = 'Assignment instructions are required';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateLesson()) {
      onSave(editedLesson);
    }
  };

  const updateContent = (updates: any) => {
    setEditedLesson(prev => ({
      ...prev,
      content: { ...prev.content, ...updates }
    }));
  };

  const addQuestion = () => {
    const newQuestion = {
      id: `question-${Date.now()}`,
      type: QuestionType.MULTIPLE_CHOICE,
      question: '',
      options: ['', '', '', ''],
      correctAnswer: 0,
      explanation: '',
      points: 1
    };

    updateContent({
      questions: [...(editedLesson.content.questions || []), newQuestion]
    });
  };

  const updateQuestion = (questionIndex: number, updates: any) => {
    const questions = [...(editedLesson.content.questions || [])];
    questions[questionIndex] = { ...questions[questionIndex], ...updates };
    updateContent({ questions });
  };

  const deleteQuestion = (questionIndex: number) => {
    const questions = [...(editedLesson.content.questions || [])];
    questions.splice(questionIndex, 1);
    updateContent({ questions });
  };

  const addInteraction = () => {
    const newInteraction = {
      id: `interaction-${Date.now()}`,
      type: InteractionType.INPUT,
      title: 'New Interaction',
      content: '',
      expectedAnswer: '',
      hint: ''
    };

    updateContent({
      interactions: [...(editedLesson.content.interactions || []), newInteraction]
    });
  };

  const updateInteraction = (interactionIndex: number, updates: any) => {
    const interactions = [...(editedLesson.content.interactions || [])];
    interactions[interactionIndex] = { ...interactions[interactionIndex], ...updates };
    updateContent({ interactions });
  };

  const deleteInteraction = (interactionIndex: number) => {
    const interactions = [...(editedLesson.content.interactions || [])];
    interactions.splice(interactionIndex, 1);
    updateContent({ interactions });
  };

  const addAttachment = () => {
    const newAttachment = {
      id: `attachment-${Date.now()}`,
      name: 'New Attachment',
      url: '',
      type: 'document',
      size: 0
    };

    updateContent({
      attachments: [...(editedLesson.content.attachments || []), newAttachment]
    });
  };

  const updateAttachment = (attachmentIndex: number, updates: any) => {
    const attachments = [...(editedLesson.content.attachments || [])];
    attachments[attachmentIndex] = { ...attachments[attachmentIndex], ...updates };
    updateContent({ attachments });
  };

  const deleteAttachment = (attachmentIndex: number) => {
    const attachments = [...(editedLesson.content.attachments || [])];
    attachments.splice(attachmentIndex, 1);
    updateContent({ attachments });
  };

  const renderContentEditor = () => {
    switch (editedLesson.type) {
      case LessonType.TEXT:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
              <textarea
                value={editedLesson.content.text || ''}
                onChange={(e) => updateContent({ text: e.target.value })}
                rows={10}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.content ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter lesson content (supports HTML)"
              />
              {errors.content && <p className="text-red-600 text-sm mt-1">{errors.content}</p>}
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">Attachments</label>
                <button
                  onClick={addAttachment}
                  className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors text-sm"
                >
                  <Plus className="w-3 h-3" />
                  <span>Add Attachment</span>
                </button>
              </div>

              <div className="space-y-2">
                {(editedLesson.content.attachments || []).map((attachment, index) => (
                  <div key={attachment.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-md">
                    <File className="w-4 h-4 text-gray-400" />
                    <div className="flex-1 space-y-2">
                      <input
                        type="text"
                        value={attachment.name}
                        onChange={(e) => updateAttachment(index, { name: e.target.value })}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        placeholder="Attachment name"
                      />
                      <input
                        type="url"
                        value={attachment.url}
                        onChange={(e) => updateAttachment(index, { url: e.target.value })}
                        className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                        placeholder="Attachment URL"
                      />
                    </div>
                    <button
                      onClick={() => deleteAttachment(index)}
                      className="text-red-400 hover:text-red-600"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case LessonType.VIDEO:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Video URL</label>
              <input
                type="url"
                value={editedLesson.content.videoUrl || ''}
                onChange={(e) => updateContent({ videoUrl: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.videoUrl ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="https://example.com/video.mp4"
              />
              {errors.videoUrl && <p className="text-red-600 text-sm mt-1">{errors.videoUrl}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Transcript (Optional)</label>
              <textarea
                value={editedLesson.content.transcript || ''}
                onChange={(e) => updateContent({ transcript: e.target.value })}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Video transcript for accessibility"
              />
            </div>
          </div>
        );

      case LessonType.QUIZ:
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium text-gray-700">Questions</label>
              <button
                onClick={addQuestion}
                className="flex items-center space-x-1 px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors text-sm"
              >
                <Plus className="w-3 h-3" />
                <span>Add Question</span>
              </button>
            </div>

            {errors.questions && <p className="text-red-600 text-sm">{errors.questions}</p>}

            <div className="space-y-4">
              {(editedLesson.content.questions || []).map((question, index) => (
                <div key={question.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900">Question {index + 1}</h4>
                    <button
                      onClick={() => deleteQuestion(index)}
                      className="text-red-400 hover:text-red-600"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Question Type</label>
                      <select
                        value={question.type}
                        onChange={(e) => updateQuestion(index, { type: e.target.value as QuestionType })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value={QuestionType.MULTIPLE_CHOICE}>Multiple Choice</option>
                        <option value={QuestionType.TRUE_FALSE}>True/False</option>
                        <option value={QuestionType.SHORT_ANSWER}>Short Answer</option>
                        <option value={QuestionType.ESSAY}>Essay</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Question</label>
                      <textarea
                        value={question.question}
                        onChange={(e) => updateQuestion(index, { question: e.target.value })}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter your question"
                      />
                    </div>

                    {question.type === QuestionType.MULTIPLE_CHOICE && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Options</label>
                        <div className="space-y-2">
                          {question.options?.map((option, optionIndex) => (
                            <div key={optionIndex} className="flex items-center space-x-2">
                              <input
                                type="radio"
                                name={`question-${index}-correct`}
                                checked={question.correctAnswer === optionIndex}
                                onChange={() => updateQuestion(index, { correctAnswer: optionIndex })}
                                className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                              />
                              <input
                                type="text"
                                value={option}
                                onChange={(e) => {
                                  const newOptions = [...(question.options || [])];
                                  newOptions[optionIndex] = e.target.value;
                                  updateQuestion(index, { options: newOptions });
                                }}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder={`Option ${optionIndex + 1}`}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {question.type === QuestionType.TRUE_FALSE && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Correct Answer</label>
                        <div className="flex space-x-4">
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name={`question-${index}-tf`}
                              checked={question.correctAnswer === 0}
                              onChange={() => updateQuestion(index, { correctAnswer: 0 })}
                              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2">True</span>
                          </label>
                          <label className="flex items-center">
                            <input
                              type="radio"
                              name={`question-${index}-tf`}
                              checked={question.correctAnswer === 1}
                              onChange={() => updateQuestion(index, { correctAnswer: 1 })}
                              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2">False</span>
                          </label>
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Points</label>
                        <input
                          type="number"
                          value={question.points || 1}
                          onChange={(e) => updateQuestion(index, { points: parseInt(e.target.value) || 1 })}
                          min="1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Explanation (Optional)</label>
                        <input
                          type="text"
                          value={question.explanation || ''}
                          onChange={(e) => updateQuestion(index, { explanation: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Explain the correct answer"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Time Limit (minutes)</label>
                <input
                  type="number"
                  value={editedLesson.content.timeLimit || 0}
                  onChange={(e) => updateContent({ timeLimit: parseInt(e.target.value) || 0 })}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0 = No limit"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Passing Score (%)</label>
                <input
                  type="number"
                  value={editedLesson.content.passingScore || 70}
                  onChange={(e) => updateContent({ passingScore: parseInt(e.target.value) || 70 })}
                  min="0"
                  max="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Attempts</label>
                <input
                  type="number"
                  value={editedLesson.content.maxAttempts || 3}
                  onChange={(e) => updateContent({ maxAttempts: parseInt(e.target.value) || 3 })}
                  min="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        );

      default:
        return <div className="text-gray-500">Content editor for {editedLesson.type} is not implemented yet.</div>;
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md ${className}`}>
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Edit Lesson</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>Save Lesson</span>
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'content', label: 'Content', icon: FileText },
            { id: 'settings', label: 'Settings', icon: Settings },
            { id: 'preview', label: 'Preview', icon: Eye }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6 max-h-96 overflow-y-auto">
        {activeTab === 'content' && (
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Lesson Title</label>
                <input
                  type="text"
                  value={editedLesson.title}
                  onChange={(e) => setEditedLesson(prev => ({ ...prev, title: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter lesson title"
                />
                {errors.title && <p className="text-red-600 text-sm mt-1">{errors.title}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Lesson Type</label>
                  <select
                    value={editedLesson.type}
                    onChange={(e) => setEditedLesson(prev => ({
                      ...prev,
                      type: e.target.value as LessonType,
                      content: { type: e.target.value as LessonType } // Reset content when type changes
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={LessonType.TEXT}>Text Content</option>
                    <option value={LessonType.VIDEO}>Video</option>
                    <option value={LessonType.QUIZ}>Quiz</option>
                    <option value={LessonType.ASSIGNMENT}>Assignment</option>
                    <option value={LessonType.INTERACTIVE}>Interactive</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Estimated Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={editedLesson.estimatedDuration || 15}
                    onChange={(e) => setEditedLesson(prev => ({
                      ...prev,
                      estimatedDuration: parseInt(e.target.value) || 15
                    }))}
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Content Editor */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Lesson Content</h3>
              {renderContentEditor()}
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Lesson Settings</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Order</label>
                  <input
                    type="number"
                    value={editedLesson.order}
                    onChange={(e) => setEditedLesson(prev => ({
                      ...prev,
                      order: parseInt(e.target.value) || 0
                    }))}
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Prerequisites</label>
                  <p className="text-sm text-gray-600 mb-2">
                    Select lessons that must be completed before this lesson becomes available.
                  </p>
                  <div className="text-sm text-gray-500">
                    Prerequisites management would be implemented here with a lesson selector.
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'preview' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Lesson Preview</h3>
              <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
                <div className="flex items-center space-x-3 mb-4">
                  {editedLesson.type === LessonType.VIDEO && <Video className="w-5 h-5 text-blue-600" />}
                  {editedLesson.type === LessonType.TEXT && <FileText className="w-5 h-5 text-green-600" />}
                  {editedLesson.type === LessonType.QUIZ && <HelpCircle className="w-5 h-5 text-purple-600" />}
                  {editedLesson.type === LessonType.ASSIGNMENT && <Target className="w-5 h-5 text-orange-600" />}
                  {editedLesson.type === LessonType.INTERACTIVE && <BookOpen className="w-5 h-5 text-indigo-600" />}

                  <div>
                    <h4 className="font-medium text-gray-900">{editedLesson.title}</h4>
                    <p className="text-sm text-gray-600">
                      {editedLesson.type} • {editedLesson.estimatedDuration || 15} minutes
                    </p>
                  </div>
                </div>

                <div className="text-sm text-gray-600">
                  Preview functionality would render the actual lesson content here based on the lesson type.
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LessonEditor;