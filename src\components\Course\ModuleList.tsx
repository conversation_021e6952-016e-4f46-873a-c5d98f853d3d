import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cle, <PERSON>, Play } from 'lucide-react';
import { Module, UserProgress, ProgressStatus } from '../../types/index';

interface ModuleListProps {
  modules: Module[];
  userProgress: UserProgress[];
  onModuleSelect?: (moduleId: string) => void;
  onLessonSelect?: (lessonId: string) => void;
  isEnrolled?: boolean;
}

const ModuleList: React.FC<ModuleListProps> = ({
  modules,
  userProgress,
  onModuleSelect,
  onLessonSelect,
  isEnrolled = false,
}) => {
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  const getModuleProgress = (module: Module): number => {
    if (module.lessons.length === 0) return 0;
    
    const completedLessons = module.lessons.filter(lesson => {
      const progress = userProgress.find(p => p.lessonId === lesson.id);
      return progress?.status === ProgressStatus.COMPLETED;
    }).length;
    
    return Math.round((completedLessons / module.lessons.length) * 100);
  };

  const canAccessModule = (module: Module): boolean => {
    if (!isEnrolled) return false;
    if (module.prerequisites.length === 0) return true;
    
    return module.prerequisites.every(prereqId => {
      const prereqModule = modules.find(m => m.id === prereqId);
      if (!prereqModule) return false;
      
      const moduleProgress = getModuleProgress(prereqModule);
      return moduleProgress === 100;
    });
  };

  const getNextLesson = (module: Module): string | null => {
    for (const lesson of module.lessons) {
      const progress = userProgress.find(p => p.lessonId === lesson.id);
      if (!progress || progress.status !== ProgressStatus.COMPLETED) {
        return lesson.id;
      }
    }
    return null;
  };

  const handleModuleClick = (module: Module) => {
    if (!canAccessModule(module)) return;
    
    if (onModuleSelect) {
      onModuleSelect(module.id);
    } else if (onLessonSelect) {
      const nextLesson = getNextLesson(module);
      if (nextLesson) {
        onLessonSelect(nextLesson);
      }
    }
  };

  return (
    <div className="space-y-4">
      {modules.map((module, index) => {
        const progress = getModuleProgress(module);
        const canAccess = canAccessModule(module);
        const isCompleted = progress === 100;
        const nextLesson = getNextLesson(module);

        return (
          <div
            key={module.id}
            className={`bg-white rounded-lg shadow-md overflow-hidden transition-all duration-200 ${
              canAccess ? 'hover:shadow-lg cursor-pointer' : 'opacity-60'
            }`}
            onClick={() => handleModuleClick(module)}
          >
            <div className="p-6">
              {/* Module Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className={`p-2 rounded-full ${
                      isCompleted ? 'bg-green-100 text-green-600' :
                      canAccess ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : canAccess ? (
                        <Play className="w-5 h-5" />
                      ) : (
                        <Lock className="w-5 h-5" />
                      )}
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      Module {index + 1}: {module.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-4">{module.description}</p>
                </div>
              </div>

              {/* Module Stats */}
              <div className="flex items-center space-x-6 mb-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <BookOpen className="w-4 h-4 mr-1" />
                  <span>{module.lessons.length} lessons</span>
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  <span>{formatDuration(module.estimatedDuration)}</span>
                </div>
                {isEnrolled && (
                  <div className="flex items-center">
                    <span className={`font-medium ${
                      isCompleted ? 'text-green-600' : 'text-blue-600'
                    }`}>
                      {progress}% complete
                    </span>
                  </div>
                )}
              </div>

              {/* Progress Bar */}
              {isEnrolled && (
                <div className="mb-4">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        isCompleted ? 'bg-green-500' : 'bg-blue-500'
                      }`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Prerequisites */}
              {module.prerequisites.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Prerequisites:</span> Complete {module.prerequisites.length} previous module(s)
                  </p>
                </div>
              )}

              {/* Action Button */}
              {isEnrolled && canAccess && (
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    {isCompleted ? (
                      <span className="text-green-600 font-medium">✓ Module completed</span>
                    ) : nextLesson ? (
                      <span>Continue with next lesson</span>
                    ) : (
                      <span>Start this module</span>
                    )}
                  </div>
                  <button
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                      isCompleted 
                        ? 'bg-green-100 text-green-700 hover:bg-green-200'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    {isCompleted ? 'Review' : 'Continue'}
                  </button>
                </div>
              )}

              {/* Locked State */}
              {isEnrolled && !canAccess && (
                <div className="flex items-center justify-center py-4 text-gray-500">
                  <Lock className="w-5 h-5 mr-2" />
                  <span>Complete previous modules to unlock</span>
                </div>
              )}

              {/* Not Enrolled State */}
              {!isEnrolled && (
                <div className="text-center py-4 text-gray-500">
                  <p>Enroll in the course to access this module</p>
                </div>
              )}
            </div>

            {/* Lesson Preview (for completed or current modules) */}
            {isEnrolled && canAccess && module.lessons.length > 0 && (
              <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-700">
                      {module.lessons.length} lesson{module.lessons.length !== 1 ? 's' : ''} in this module
                    </p>
                    <p className="text-xs text-gray-500">
                      {module.lessons.filter(lesson => {
                        const progress = userProgress.find(p => p.lessonId === lesson.id);
                        return progress?.status === ProgressStatus.COMPLETED;
                      }).length} completed
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">
                      Est. {formatDuration(module.estimatedDuration)}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default ModuleList;
