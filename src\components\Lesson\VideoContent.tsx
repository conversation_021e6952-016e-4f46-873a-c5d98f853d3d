import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Maximize, CheckCircle, FileText, Download } from 'lucide-react';
import { VideoContent as VideoContentType } from '../../types/index';

interface VideoContentProps {
  content: VideoContentType;
  onComplete: () => void;
  isCompleted: boolean;
}

const VideoContent: React.FC<VideoContentProps> = ({
  content,
  onComplete,
  isCompleted,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [watchedPercentage, setWatchedPercentage] = useState(0);
  const [showTranscript, setShowTranscript] = useState(false);
  const [hasWatchedEnough, setHasWatchedEnough] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
      const percentage = (video.currentTime / video.duration) * 100;
      setWatchedPercentage(percentage);
      
      // Consider video "watched enough" when user has watched 80% of it
      if (percentage >= 80 && !hasWatchedEnough) {
        setHasWatchedEnough(true);
      }
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setHasWatchedEnough(true);
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('ended', handleEnded);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('ended', handleEnded);
    };
  }, [hasWatchedEnough]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newTime = (parseFloat(e.target.value) / 100) * duration;
    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = parseFloat(e.target.value) / 100;
    video.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isMuted) {
      video.volume = volume;
      setIsMuted(false);
    } else {
      video.volume = 0;
      setIsMuted(true);
    }
  };

  const toggleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      video.requestFullscreen();
    }
  };

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleMarkComplete = () => {
    if (!isCompleted && hasWatchedEnough) {
      onComplete();
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="p-6">
      {/* Video Player */}
      <div className="relative bg-black rounded-lg overflow-hidden mb-6">
        <video
          ref={videoRef}
          className="w-full aspect-video"
          src={content.videoUrl}
          poster="https://via.placeholder.com/800x450/000000/ffffff?text=Video+Loading..."
        />
        
        {/* Video Controls */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          {/* Progress Bar */}
          <div className="mb-4">
            <input
              type="range"
              min="0"
              max="100"
              value={duration ? (currentTime / duration) * 100 : 0}
              onChange={handleSeek}
              className="w-full h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
          
          {/* Control Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={togglePlay}
                className="text-white hover:text-blue-400 transition-colors"
              >
                {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
              </button>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleMute}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                </button>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={isMuted ? 0 : volume * 100}
                  onChange={handleVolumeChange}
                  className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                />
              </div>
              
              <span className="text-white text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>
            
            <button
              onClick={toggleFullscreen}
              className="text-white hover:text-blue-400 transition-colors"
            >
              <Maximize className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Watch Progress</span>
          <span className="text-sm text-gray-600">{Math.round(watchedPercentage)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              hasWatchedEnough ? 'bg-green-500' : 'bg-blue-500'
            }`}
            style={{ width: `${watchedPercentage}%` }}
          ></div>
        </div>
        {hasWatchedEnough && (
          <p className="text-sm text-green-600 mt-2">
            ✓ You've watched enough of this video to complete the lesson!
          </p>
        )}
      </div>

      {/* Transcript */}
      {content.transcript && (
        <div className="mb-6">
          <button
            onClick={() => setShowTranscript(!showTranscript)}
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors mb-4"
          >
            <FileText className="w-4 h-4" />
            <span>{showTranscript ? 'Hide' : 'Show'} Transcript</span>
          </button>
          
          {showTranscript && (
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h4 className="font-medium text-gray-900 mb-3">Video Transcript</h4>
              <div className="prose prose-sm max-w-none text-gray-700 leading-relaxed">
                {content.transcript.split('\n').map((paragraph, index) => (
                  <p key={index} className="mb-2">{paragraph}</p>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Attachments */}
      {content.attachments && content.attachments.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Resources</h3>
          <div className="space-y-3">
            {content.attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{attachment.name}</p>
                    <p className="text-sm text-gray-600">
                      {attachment.type} • {formatFileSize(attachment.size)}
                    </p>
                  </div>
                </div>
                <a
                  href={attachment.url}
                  download={attachment.name}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </a>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Completion Section */}
      <div className="border-t border-gray-200 pt-6">
        {!isCompleted ? (
          <div className="text-center">
            {!hasWatchedEnough ? (
              <div className="mb-4">
                <p className="text-gray-600 mb-2">
                  Watch at least 80% of the video to complete this lesson.
                </p>
                <p className="text-sm text-gray-500">
                  Current progress: {Math.round(watchedPercentage)}%
                </p>
              </div>
            ) : (
              <div className="mb-4">
                <div className="flex items-center justify-center space-x-2 text-green-600 mb-2">
                  <CheckCircle className="w-5 h-5" />
                  <span>You've watched enough of this video!</span>
                </div>
              </div>
            )}
            
            <button
              onClick={handleMarkComplete}
              disabled={!hasWatchedEnough}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                hasWatchedEnough
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              Mark as Complete
            </button>
          </div>
        ) : (
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 text-green-600 mb-4">
              <CheckCircle className="w-6 h-6" />
              <span className="text-lg font-medium">Lesson Completed!</span>
            </div>
            <p className="text-gray-600">
              Great job! You can now proceed to the next lesson.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoContent;
