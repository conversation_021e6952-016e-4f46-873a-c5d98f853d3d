import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Clock,
  User,
  BookOpen,
  Play,
  FileText,
  HelpCircle,
  ClipboardList,
  ChevronDown,
  ChevronRight,
  Lock,
  CheckCircle,
  ArrowLeft,
  Users,
  Star
} from 'lucide-react';
import { Course, Module, Lesson, CourseEnrollment, UserProgress, LessonType, ProgressStatus } from '../../types/index';
import { useCourse } from '../../contexts/CourseContext';
import { useLesson } from '../../contexts/LessonContext';
import { useAuth } from '../../contexts/AuthContext';

interface CourseDetailProps {
  className?: string;
}

const CourseDetail: React.FC<CourseDetailProps> = ({
  className = '',
}) => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const { state: courseState, actions: courseActions } = useCourse();
  const { state: lessonState, canAccessLesson } = useLesson();
  const { user } = useAuth();

  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());
  const [course, setCourse] = useState<Course | null>(null);
  const [enrollment, setEnrollment] = useState<CourseEnrollment | null>(null);

  useEffect(() => {
    if (courseId) {
      courseActions.loadCourse(courseId);
    }
  }, [courseId]);

  useEffect(() => {
    if (courseState.activeCourse) {
      setCourse(courseState.activeCourse);
      // Find enrollment for this course
      const userEnrollment = courseState.enrollments.find(e => e.courseId === courseId);
      setEnrollment(userEnrollment || null);
      
      // Expand first module by default
      if (courseState.activeCourse.modules.length > 0) {
        setExpandedModules(new Set([courseState.activeCourse.modules[0].id]));
      }
    }
  }, [courseState.activeCourse, courseState.enrollments, courseId]);

  const toggleModule = (moduleId: string) => {
    setExpandedModules(prev => {
      const newSet = new Set(prev);
      if (newSet.has(moduleId)) {
        newSet.delete(moduleId);
      } else {
        newSet.add(moduleId);
      }
      return newSet;
    });
  };

  const handleEnroll = async () => {
    if (courseId) {
      await courseActions.enrollInCourse(courseId);
    }
  };

  const handleBackToCourses = () => {
    navigate('/courses');
  };

  const handleLessonClick = (lesson: Lesson) => {
    if (canAccessLesson(lesson, lessonState.progress)) {
      navigate(`/lessons/${lesson.id}`);
    }
  };

  const getLessonIcon = (type: LessonType) => {
    switch (type) {
      case LessonType.VIDEO:
        return <Play className="w-4 h-4" />;
      case LessonType.TEXT:
        return <FileText className="w-4 h-4" />;
      case LessonType.QUIZ:
        return <HelpCircle className="w-4 h-4" />;
      case LessonType.ASSIGNMENT:
        return <ClipboardList className="w-4 h-4" />;
      case LessonType.INTERACTIVE:
        return <BookOpen className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getLessonProgress = (lessonId: string): UserProgress | undefined => {
    return lessonState.progress.find(p => p.lessonId === lessonId);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes}m`;
    } else if (remainingMinutes === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${remainingMinutes}m`;
    }
  };

  const calculateModuleProgress = (module: Module): number => {
    if (module.lessons.length === 0) return 0;
    
    const completedLessons = module.lessons.filter(lesson => {
      const progress = getLessonProgress(lesson.id);
      return progress?.status === ProgressStatus.COMPLETED;
    }).length;
    
    return Math.round((completedLessons / module.lessons.length) * 100);
  };

  if (courseState.isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading course...</span>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Course not found</p>
      </div>
    );
  }

  const isEnrolled = !!enrollment;
  const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);
  const completedLessons = course.modules.reduce((total, module) => {
    return total + module.lessons.filter(lesson => {
      const progress = getLessonProgress(lesson.id);
      return progress?.status === ProgressStatus.COMPLETED;
    }).length;
  }, 0);

  return (
    <div className={`max-w-4xl mx-auto space-y-8 ${className}`}>
      {/* Back Button */}
      <button
        onClick={handleBackToCourses}
        className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
      >
        <ArrowLeft className="w-4 h-4" />
        <span>Back to Courses</span>
      </button>

      {/* Course Header */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="md:flex">
          <div className="md:w-1/3">
            <img
              src={course.thumbnail || 'https://via.placeholder.com/400x250/f3f4f6/9ca3af?text=Course+Image'}
              alt={course.title}
              className="w-full h-64 md:h-full object-cover"
            />
          </div>
          <div className="md:w-2/3 p-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>
            <p className="text-gray-600 mb-6">{course.description}</p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <User className="w-6 h-6 mx-auto mb-1 text-gray-400" />
                <p className="text-sm text-gray-600">{course.instructor.name}</p>
              </div>
              <div className="text-center">
                <Clock className="w-6 h-6 mx-auto mb-1 text-gray-400" />
                <p className="text-sm text-gray-600">{formatDuration(course.estimatedDuration)}</p>
              </div>
              <div className="text-center">
                <BookOpen className="w-6 h-6 mx-auto mb-1 text-gray-400" />
                <p className="text-sm text-gray-600">{course.modules.length} modules</p>
              </div>
              <div className="text-center">
                <FileText className="w-6 h-6 mx-auto mb-1 text-gray-400" />
                <p className="text-sm text-gray-600">{totalLessons} lessons</p>
              </div>
            </div>

            {isEnrolled ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Progress</span>
                  <span className="text-sm text-gray-600">{completedLessons}/{totalLessons} lessons completed</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${enrollment?.progress || 0}%` }}
                  ></div>
                </div>
              </div>
            ) : (
              <button
                onClick={handleEnroll}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium"
              >
                Enroll in Course
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Course Content</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {course.modules.map((module, moduleIndex) => {
            const isExpanded = expandedModules.has(module.id);
            const moduleProgress = calculateModuleProgress(module);
            
            return (
              <div key={module.id} className="p-6">
                <button
                  onClick={() => toggleModule(module.id)}
                  className="w-full flex items-center justify-between text-left hover:bg-gray-50 p-2 rounded-md transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    {isExpanded ? (
                      <ChevronDown className="w-5 h-5 text-gray-400" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    )}
                    <div>
                      <h3 className="font-medium text-gray-900">
                        Module {moduleIndex + 1}: {module.title}
                      </h3>
                      <p className="text-sm text-gray-600">{module.description}</p>
                      <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                        <span>{module.lessons.length} lessons</span>
                        <span>{formatDuration(module.estimatedDuration)}</span>
                        {isEnrolled && (
                          <span className="text-blue-600">{moduleProgress}% complete</span>
                        )}
                      </div>
                    </div>
                  </div>
                </button>

                {isExpanded && (
                  <div className="mt-4 ml-8 space-y-2">
                    {module.lessons.map((lesson, lessonIndex) => {
                      const progress = getLessonProgress(lesson.id);
                      const isCompleted = progress?.status === ProgressStatus.COMPLETED;
                      const canAccess = canAccessLesson(lesson, lessonState.progress);
                      const isLocked = !canAccess && isEnrolled;

                      return (
                        <div
                          key={lesson.id}
                          className={`flex items-center justify-between p-3 rounded-md border transition-colors ${
                            canAccess && isEnrolled
                              ? 'hover:bg-blue-50 cursor-pointer border-gray-200'
                              : 'border-gray-100'
                          } ${isCompleted ? 'bg-green-50 border-green-200' : ''}`}
                          onClick={() => canAccess && isEnrolled && handleLessonClick(lesson)}
                        >
                          <div className="flex items-center space-x-3">
                            <div className={`p-1 rounded ${
                              isCompleted ? 'text-green-600' : 
                              isLocked ? 'text-gray-400' : 'text-blue-600'
                            }`}>
                              {isCompleted ? (
                                <CheckCircle className="w-4 h-4" />
                              ) : isLocked ? (
                                <Lock className="w-4 h-4" />
                              ) : (
                                getLessonIcon(lesson.type)
                              )}
                            </div>
                            <div>
                              <p className={`font-medium ${
                                isLocked ? 'text-gray-400' : 'text-gray-900'
                              }`}>
                                {lessonIndex + 1}. {lesson.title}
                              </p>
                              <p className={`text-sm ${
                                isLocked ? 'text-gray-400' : 'text-gray-600'
                              }`}>
                                {lesson.description}
                              </p>
                            </div>
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatDuration(lesson.estimatedDuration)}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Tags */}
      {course.tags.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Topics Covered</h3>
          <div className="flex flex-wrap gap-2">
            {course.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CourseDetail;
