import React from 'react';
import { <PERSON>, CheckCircle, AlertCircle, <PERSON>O<PERSON>, Clock } from 'lucide-react';
import { Course, Module, Lesson, UserProgress, ProgressStatus } from '../../types/index';

interface PrerequisiteCheckerProps {
  item: Course | Module | Lesson;
  itemType: 'course' | 'module' | 'lesson';
  userProgress: UserProgress[];
  allCourses?: Course[];
  currentCourse?: Course;
  onPrerequisiteClick?: (id: string, type: 'course' | 'module' | 'lesson') => void;
  className?: string;
}

const PrerequisiteChecker: React.FC<PrerequisiteCheckerProps> = ({
  item,
  itemType,
  userProgress,
  allCourses = [],
  currentCourse,
  onPrerequisiteClick,
  className = '',
}) => {
  // Check if prerequisites are met
  const checkPrerequisites = () => {
    if (!item.prerequisites || item.prerequisites.length === 0) {
      return { canAccess: true, unmetPrerequisites: [] };
    }

    const unmetPrerequisites: Array<{
      id: string;
      title: string;
      type: 'course' | 'module' | 'lesson';
      progress: number;
      isCompleted: boolean;
    }> = [];

    item.prerequisites.forEach(prereqId => {
      let prerequisite: any = null;
      let prereqType: 'course' | 'module' | 'lesson' = 'lesson';
      let progress = 0;
      let isCompleted = false;

      // Check if it's a course prerequisite
      const course = allCourses.find(c => c.id === prereqId);
      if (course) {
        prerequisite = course;
        prereqType = 'course';
        
        // Calculate course completion
        const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);
        const completedLessons = userProgress.filter(p => 
          course.modules.some(m => m.lessons.some(l => l.id === p.lessonId)) &&
          p.status === ProgressStatus.COMPLETED
        ).length;
        progress = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
        isCompleted = progress >= 100;
      } else if (currentCourse) {
        // Check if it's a module prerequisite
        const module = currentCourse.modules.find(m => m.id === prereqId);
        if (module) {
          prerequisite = module;
          prereqType = 'module';
          
          // Calculate module completion
          const completedLessons = userProgress.filter(p => 
            module.lessons.some(l => l.id === p.lessonId) &&
            p.status === ProgressStatus.COMPLETED
          ).length;
          progress = module.lessons.length > 0 ? (completedLessons / module.lessons.length) * 100 : 0;
          isCompleted = progress >= 100;
        } else {
          // Check if it's a lesson prerequisite
          const lesson = currentCourse.modules
            .flatMap(m => m.lessons)
            .find(l => l.id === prereqId);
          
          if (lesson) {
            prerequisite = lesson;
            prereqType = 'lesson';
            
            const lessonProgress = userProgress.find(p => p.lessonId === lesson.id);
            isCompleted = lessonProgress?.status === ProgressStatus.COMPLETED;
            progress = isCompleted ? 100 : (lessonProgress ? 50 : 0);
          }
        }
      }

      if (prerequisite && !isCompleted) {
        unmetPrerequisites.push({
          id: prereqId,
          title: prerequisite.title,
          type: prereqType,
          progress,
          isCompleted
        });
      }
    });

    return {
      canAccess: unmetPrerequisites.length === 0,
      unmetPrerequisites
    };
  };

  const { canAccess, unmetPrerequisites } = checkPrerequisites();

  const getTypeIcon = (type: 'course' | 'module' | 'lesson') => {
    switch (type) {
      case 'course':
        return <BookOpen className="w-4 h-4" />;
      case 'module':
        return <BookOpen className="w-4 h-4" />;
      case 'lesson':
        return <Clock className="w-4 h-4" />;
    }
  };

  const getTypeLabel = (type: 'course' | 'module' | 'lesson') => {
    switch (type) {
      case 'course':
        return 'Course';
      case 'module':
        return 'Module';
      case 'lesson':
        return 'Lesson';
    }
  };

  if (canAccess) {
    return null; // No need to show anything if access is granted
  }

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <Lock className="w-5 h-5 text-yellow-600 mt-0.5" />
        </div>
        <div className="flex-1">
          <h4 className="text-sm font-medium text-yellow-800 mb-2">
            Prerequisites Required
          </h4>
          <p className="text-sm text-yellow-700 mb-3">
            Complete the following {unmetPrerequisites.length === 1 ? 'requirement' : 'requirements'} to access this {itemType}:
          </p>
          
          <div className="space-y-2">
            {unmetPrerequisites.map((prereq) => (
              <div
                key={prereq.id}
                className={`flex items-center justify-between p-3 bg-white rounded-md border border-yellow-200 ${
                  onPrerequisiteClick ? 'cursor-pointer hover:bg-yellow-50' : ''
                }`}
                onClick={() => onPrerequisiteClick?.(prereq.id, prereq.type)}
              >
                <div className="flex items-center space-x-3">
                  <div className="text-yellow-600">
                    {getTypeIcon(prereq.type)}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {prereq.title}
                    </p>
                    <p className="text-xs text-gray-600">
                      {getTypeLabel(prereq.type)} • {Math.round(prereq.progress)}% complete
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {prereq.progress > 0 ? (
                    <div className="flex items-center space-x-1 text-blue-600">
                      <Clock className="w-4 h-4" />
                      <span className="text-xs">In Progress</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 text-gray-500">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-xs">Not Started</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          {onPrerequisiteClick && (
            <p className="text-xs text-yellow-600 mt-3">
              Click on any prerequisite to navigate to it.
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

interface PrerequisiteStatusProps {
  item: Course | Module | Lesson;
  itemType: 'course' | 'module' | 'lesson';
  userProgress: UserProgress[];
  allCourses?: Course[];
  currentCourse?: Course;
  showDetails?: boolean;
  className?: string;
}

export const PrerequisiteStatus: React.FC<PrerequisiteStatusProps> = ({
  item,
  itemType,
  userProgress,
  allCourses = [],
  currentCourse,
  showDetails = false,
  className = '',
}) => {
  if (!item.prerequisites || item.prerequisites.length === 0) {
    return null;
  }

  const checkPrerequisites = () => {
    const prerequisites: Array<{
      id: string;
      title: string;
      type: 'course' | 'module' | 'lesson';
      isCompleted: boolean;
    }> = [];

    item.prerequisites.forEach(prereqId => {
      let prerequisite: any = null;
      let prereqType: 'course' | 'module' | 'lesson' = 'lesson';
      let isCompleted = false;

      // Check course prerequisite
      const course = allCourses.find(c => c.id === prereqId);
      if (course) {
        prerequisite = course;
        prereqType = 'course';
        
        const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);
        const completedLessons = userProgress.filter(p => 
          course.modules.some(m => m.lessons.some(l => l.id === p.lessonId)) &&
          p.status === ProgressStatus.COMPLETED
        ).length;
        isCompleted = totalLessons > 0 && completedLessons >= totalLessons;
      } else if (currentCourse) {
        // Check module prerequisite
        const module = currentCourse.modules.find(m => m.id === prereqId);
        if (module) {
          prerequisite = module;
          prereqType = 'module';
          
          const completedLessons = userProgress.filter(p => 
            module.lessons.some(l => l.id === p.lessonId) &&
            p.status === ProgressStatus.COMPLETED
          ).length;
          isCompleted = module.lessons.length > 0 && completedLessons >= module.lessons.length;
        } else {
          // Check lesson prerequisite
          const lesson = currentCourse.modules
            .flatMap(m => m.lessons)
            .find(l => l.id === prereqId);
          
          if (lesson) {
            prerequisite = lesson;
            prereqType = 'lesson';
            
            const lessonProgress = userProgress.find(p => p.lessonId === lesson.id);
            isCompleted = lessonProgress?.status === ProgressStatus.COMPLETED;
          }
        }
      }

      if (prerequisite) {
        prerequisites.push({
          id: prereqId,
          title: prerequisite.title,
          type: prereqType,
          isCompleted
        });
      }
    });

    const completedCount = prerequisites.filter(p => p.isCompleted).length;
    const allCompleted = completedCount === prerequisites.length;

    return { prerequisites, completedCount, allCompleted };
  };

  const { prerequisites, completedCount, allCompleted } = checkPrerequisites();

  return (
    <div className={`text-sm ${className}`}>
      <div className="flex items-center space-x-2 mb-2">
        {allCompleted ? (
          <CheckCircle className="w-4 h-4 text-green-600" />
        ) : (
          <Lock className="w-4 h-4 text-yellow-600" />
        )}
        <span className={`font-medium ${allCompleted ? 'text-green-700' : 'text-yellow-700'}`}>
          Prerequisites: {completedCount}/{prerequisites.length} completed
        </span>
      </div>
      
      {showDetails && (
        <div className="space-y-1 ml-6">
          {prerequisites.map((prereq) => (
            <div key={prereq.id} className="flex items-center space-x-2">
              {prereq.isCompleted ? (
                <CheckCircle className="w-3 h-3 text-green-600" />
              ) : (
                <div className="w-3 h-3 border border-gray-300 rounded-full" />
              )}
              <span className={`text-xs ${prereq.isCompleted ? 'text-green-700' : 'text-gray-600'}`}>
                {prereq.title} ({getTypeLabel(prereq.type)})
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const getTypeLabel = (type: 'course' | 'module' | 'lesson') => {
  switch (type) {
    case 'course':
      return 'Course';
    case 'module':
      return 'Module';
    case 'lesson':
      return 'Lesson';
  }
};

export default PrerequisiteChecker;
