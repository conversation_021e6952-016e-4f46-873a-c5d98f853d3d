import React, { useState } from 'react';
import { 
  Ch<PERSON>ronRight, 
  Check<PERSON>ircle, 
  Lock, 
  Clock, 
  BookOpen, 
  Target,
  ArrowRight,
  Eye,
  EyeOff
} from 'lucide-react';
import { Course, Module, Lesson, UserProgress, ProgressStatus } from '../../types/index';

interface LearningPathProps {
  course: Course;
  userProgress: UserProgress[];
  currentLessonId?: string;
  onItemClick?: (id: string, type: 'module' | 'lesson') => void;
  showCompletedItems?: boolean;
  className?: string;
}

const LearningPath: React.FC<LearningPathProps> = ({
  course,
  userProgress,
  currentLessonId,
  onItemClick,
  showCompletedItems = true,
  className = '',
}) => {
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());
  const [showCompleted, setShowCompleted] = useState(showCompletedItems);

  // Helper function to check if prerequisites are met
  const checkPrerequisites = (prerequisites: string[] | undefined): boolean => {
    if (!prerequisites || prerequisites.length === 0) return true;
    
    return prerequisites.every(prereqId => {
      // Check if it's a lesson prerequisite
      const lessonProgress = userProgress.find(p => p.lessonId === prereqId);
      if (lessonProgress) {
        return lessonProgress.status === ProgressStatus.COMPLETED;
      }
      
      // Check if it's a module prerequisite
      const module = course.modules.find(m => m.id === prereqId);
      if (module) {
        const moduleCompletedLessons = userProgress.filter(p => 
          module.lessons.some(l => l.id === p.lessonId) &&
          p.status === ProgressStatus.COMPLETED
        ).length;
        return moduleCompletedLessons >= module.lessons.length;
      }
      
      return false;
    });
  };

  // Get lesson status
  const getLessonStatus = (lesson: Lesson) => {
    const progress = userProgress.find(p => p.lessonId === lesson.id);
    const prerequisitesMet = checkPrerequisites(lesson.prerequisites);
    
    if (progress?.status === ProgressStatus.COMPLETED) {
      return { status: 'completed', canAccess: true, progress: progress };
    } else if (progress?.status === ProgressStatus.IN_PROGRESS) {
      return { status: 'in-progress', canAccess: prerequisitesMet, progress: progress };
    } else if (prerequisitesMet) {
      return { status: 'available', canAccess: true, progress: null };
    } else {
      return { status: 'locked', canAccess: false, progress: null };
    }
  };

  // Get module status
  const getModuleStatus = (module: Module) => {
    const completedLessons = module.lessons.filter(lesson => {
      const progress = userProgress.find(p => p.lessonId === lesson.id);
      return progress?.status === ProgressStatus.COMPLETED;
    }).length;
    
    const prerequisitesMet = checkPrerequisites(module.prerequisites);
    const totalLessons = module.lessons.length;
    const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
    
    if (completedLessons === totalLessons && totalLessons > 0) {
      return { status: 'completed', canAccess: true, progress: progressPercentage };
    } else if (completedLessons > 0) {
      return { status: 'in-progress', canAccess: prerequisitesMet, progress: progressPercentage };
    } else if (prerequisitesMet) {
      return { status: 'available', canAccess: true, progress: 0 };
    } else {
      return { status: 'locked', canAccess: false, progress: 0 };
    }
  };

  const toggleModule = (moduleId: string) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(moduleId)) {
      newExpanded.delete(moduleId);
    } else {
      newExpanded.add(moduleId);
    }
    setExpandedModules(newExpanded);
  };

  const getStatusIcon = (status: string, isCurrent: boolean = false) => {
    if (isCurrent) {
      return <Target className="w-5 h-5 text-blue-600" />;
    }
    
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'in-progress':
        return <Clock className="w-5 h-5 text-blue-600" />;
      case 'available':
        return <div className="w-5 h-5 border-2 border-gray-400 rounded-full" />;
      case 'locked':
        return <Lock className="w-5 h-5 text-gray-400" />;
      default:
        return <div className="w-5 h-5 border-2 border-gray-300 rounded-full" />;
    }
  };

  const getStatusStyles = (status: string, canAccess: boolean, isCurrent: boolean = false) => {
    if (isCurrent) {
      return 'bg-blue-50 border-blue-200 text-blue-900';
    }
    
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200 text-green-900';
      case 'in-progress':
        return 'bg-blue-50 border-blue-200 text-blue-900';
      case 'available':
        return canAccess 
          ? 'bg-white border-gray-200 text-gray-900 hover:bg-gray-50' 
          : 'bg-gray-50 border-gray-200 text-gray-500';
      case 'locked':
        return 'bg-gray-50 border-gray-200 text-gray-400';
      default:
        return 'bg-white border-gray-200 text-gray-900';
    }
  };

  // Filter modules and lessons based on showCompleted setting
  const filteredModules = course.modules.filter(module => {
    if (showCompleted) return true;
    const moduleStatus = getModuleStatus(module);
    return moduleStatus.status !== 'completed';
  });

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Learning Path</h3>
        <button
          onClick={() => setShowCompleted(!showCompleted)}
          className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
        >
          {showCompleted ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          <span>{showCompleted ? 'Hide' : 'Show'} Completed</span>
        </button>
      </div>

      {/* Learning Path */}
      <div className="space-y-3">
        {filteredModules.map((module, moduleIndex) => {
          const moduleStatus = getModuleStatus(module);
          const isExpanded = expandedModules.has(module.id);
          
          // Filter lessons if not showing completed
          const filteredLessons = module.lessons.filter(lesson => {
            if (showCompleted) return true;
            const lessonStatus = getLessonStatus(lesson);
            return lessonStatus.status !== 'completed';
          });

          return (
            <div key={module.id} className="border border-gray-200 rounded-lg overflow-hidden">
              {/* Module Header */}
              <div
                className={`p-4 border-l-4 cursor-pointer transition-colors ${
                  moduleStatus.status === 'completed' ? 'border-l-green-500' :
                  moduleStatus.status === 'in-progress' ? 'border-l-blue-500' :
                  moduleStatus.status === 'available' ? 'border-l-gray-400' :
                  'border-l-gray-300'
                } ${getStatusStyles(moduleStatus.status, moduleStatus.canAccess)}`}
                onClick={() => {
                  toggleModule(module.id);
                  if (moduleStatus.canAccess && onItemClick) {
                    onItemClick(module.id, 'module');
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(moduleStatus.status)}
                    <div>
                      <h4 className="font-medium">
                        Module {moduleIndex + 1}: {module.title}
                      </h4>
                      <p className="text-sm opacity-75 mt-1">{module.description}</p>
                      <div className="flex items-center space-x-4 text-xs mt-2">
                        <span>{module.lessons.length} lessons</span>
                        <span>{Math.round(moduleStatus.progress)}% complete</span>
                        {module.estimatedDuration && (
                          <span>{module.estimatedDuration} min</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <ChevronRight 
                    className={`w-5 h-5 transition-transform ${isExpanded ? 'rotate-90' : ''}`} 
                  />
                </div>
              </div>

              {/* Module Lessons */}
              {isExpanded && (
                <div className="bg-gray-50 border-t border-gray-200">
                  {filteredLessons.map((lesson, lessonIndex) => {
                    const lessonStatus = getLessonStatus(lesson);
                    const isCurrent = lesson.id === currentLessonId;
                    
                    return (
                      <div
                        key={lesson.id}
                        className={`p-3 border-b border-gray-200 last:border-b-0 cursor-pointer transition-colors ${
                          getStatusStyles(lessonStatus.status, lessonStatus.canAccess, isCurrent)
                        }`}
                        onClick={() => {
                          if (lessonStatus.canAccess && onItemClick) {
                            onItemClick(lesson.id, 'lesson');
                          }
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(lessonStatus.status, isCurrent)}
                            <div>
                              <p className="font-medium text-sm">
                                {lessonIndex + 1}. {lesson.title}
                              </p>
                              <div className="flex items-center space-x-3 text-xs mt-1 opacity-75">
                                <span>{lesson.type}</span>
                                {lesson.estimatedDuration && (
                                  <span>{lesson.estimatedDuration} min</span>
                                )}
                                {lessonStatus.progress && (
                                  <span>{lessonStatus.progress.timeSpent}m spent</span>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          {!lessonStatus.canAccess && (
                            <div className="text-xs text-gray-500">
                              Prerequisites required
                            </div>
                          )}
                        </div>
                        
                        {/* Prerequisites indicator */}
                        {lesson.prerequisites && lesson.prerequisites.length > 0 && (
                          <div className="mt-2 ml-8 text-xs text-gray-600">
                            <div className="flex items-center space-x-1">
                              <ArrowRight className="w-3 h-3" />
                              <span>
                                Requires: {lesson.prerequisites.length} prerequisite{lesson.prerequisites.length !== 1 ? 's' : ''}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                  
                  {filteredLessons.length === 0 && !showCompleted && (
                    <div className="p-4 text-center text-gray-500 text-sm">
                      All lessons in this module are completed
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
        
        {filteredModules.length === 0 && !showCompleted && (
          <div className="text-center py-8 text-gray-500">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-400" />
            <p className="text-lg font-medium">Congratulations!</p>
            <p className="text-sm mt-1">You've completed all modules in this course.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default LearningPath;
