import React, { useState, useEffect } from 'react';
import { CheckCircle, RotateCcw, Lightbulb, Target, Award } from 'lucide-react';
import { InteractiveContent as InteractiveContentType } from '../../types/index';

interface InteractiveContentProps {
  content: InteractiveContentType;
  onComplete: () => void;
  isCompleted: boolean;
}

const InteractiveContent: React.FC<InteractiveContentProps> = ({
  content,
  onComplete,
  isCompleted,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [userInputs, setUserInputs] = useState<Record<string, any>>({});
  const [showHints, setShowHints] = useState<Record<number, boolean>>({});
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    // Check if all steps are completed
    if (completedSteps.size === content.steps.length && content.steps.length > 0) {
      onComplete();
    }
  }, [completedSteps, content.steps.length, onComplete]);

  const handleStart = () => {
    setHasStarted(true);
    setCurrentStep(0);
    setUserInputs({});
    setCompletedSteps(new Set());
    setShowHints({});
  };

  const handleStepComplete = (stepIndex: number) => {
    setCompletedSteps(prev => new Set([...prev, stepIndex]));
    
    // Move to next step if available
    if (stepIndex < content.steps.length - 1) {
      setCurrentStep(stepIndex + 1);
    }
  };

  const handleInputChange = (stepId: string, value: any) => {
    setUserInputs(prev => ({
      ...prev,
      [stepId]: value
    }));
  };

  const toggleHint = (stepIndex: number) => {
    setShowHints(prev => ({
      ...prev,
      [stepIndex]: !prev[stepIndex]
    }));
  };

  const resetActivity = () => {
    setCurrentStep(0);
    setUserInputs({});
    setCompletedSteps(new Set());
    setShowHints({});
    setHasStarted(false);
  };

  const renderStepContent = (step: any, stepIndex: number) => {
    const isCurrentStep = stepIndex === currentStep;
    const isCompleted = completedSteps.has(stepIndex);
    const canAccess = stepIndex === 0 || completedSteps.has(stepIndex - 1);

    switch (step.type) {
      case 'input':
        return (
          <div className="space-y-4">
            <div className="prose prose-lg max-w-none">
              <div dangerouslySetInnerHTML={{ __html: step.content }} />
            </div>
            
            {step.inputType === 'text' && (
              <input
                type="text"
                value={userInputs[step.id] || ''}
                onChange={(e) => handleInputChange(step.id, e.target.value)}
                placeholder={step.placeholder || 'Enter your answer...'}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={!canAccess || isCompleted}
              />
            )}
            
            {step.inputType === 'textarea' && (
              <textarea
                value={userInputs[step.id] || ''}
                onChange={(e) => handleInputChange(step.id, e.target.value)}
                placeholder={step.placeholder || 'Enter your response...'}
                rows={4}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                disabled={!canAccess || isCompleted}
              />
            )}
            
            {step.inputType === 'select' && (
              <select
                value={userInputs[step.id] || ''}
                onChange={(e) => handleInputChange(step.id, e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={!canAccess || isCompleted}
              >
                <option value="">Select an option...</option>
                {step.options?.map((option: string, index: number) => (
                  <option key={index} value={option}>{option}</option>
                ))}
              </select>
            )}
            
            {step.inputType === 'radio' && (
              <div className="space-y-2">
                {step.options?.map((option: string, index: number) => (
                  <label key={index} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name={step.id}
                      value={option}
                      checked={userInputs[step.id] === option}
                      onChange={(e) => handleInputChange(step.id, e.target.value)}
                      disabled={!canAccess || isCompleted}
                      className="text-blue-600"
                    />
                    <span className="text-gray-700">{option}</span>
                  </label>
                ))}
              </div>
            )}
            
            {canAccess && !isCompleted && (
              <button
                onClick={() => handleStepComplete(stepIndex)}
                disabled={!userInputs[step.id]}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  userInputs[step.id]
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Complete Step
              </button>
            )}
          </div>
        );

      case 'content':
        return (
          <div className="space-y-4">
            <div className="prose prose-lg max-w-none">
              <div dangerouslySetInnerHTML={{ __html: step.content }} />
            </div>
            
            {step.media && (
              <div className="mt-4">
                {step.media.type === 'image' && (
                  <img
                    src={step.media.url}
                    alt={step.media.alt || 'Interactive content'}
                    className="max-w-full h-auto rounded-lg shadow-md"
                  />
                )}
                {step.media.type === 'video' && (
                  <video
                    src={step.media.url}
                    controls
                    className="w-full rounded-lg shadow-md"
                  />
                )}
              </div>
            )}
            
            {canAccess && !isCompleted && (
              <button
                onClick={() => handleStepComplete(stepIndex)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
              >
                Continue
              </button>
            )}
          </div>
        );

      case 'quiz':
        return (
          <div className="space-y-4">
            <div className="prose prose-lg max-w-none">
              <div dangerouslySetInnerHTML={{ __html: step.question }} />
            </div>
            
            <div className="space-y-2">
              {step.options?.map((option: string, index: number) => (
                <label key={index} className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name={step.id}
                    value={option}
                    checked={userInputs[step.id] === option}
                    onChange={(e) => handleInputChange(step.id, e.target.value)}
                    disabled={!canAccess || isCompleted}
                    className="text-blue-600"
                  />
                  <span className="text-gray-700">{option}</span>
                </label>
              ))}
            </div>
            
            {userInputs[step.id] && isCompleted && (
              <div className={`p-3 rounded-lg ${
                userInputs[step.id] === step.correctAnswer
                  ? 'bg-green-50 border border-green-200 text-green-800'
                  : 'bg-red-50 border border-red-200 text-red-800'
              }`}>
                {userInputs[step.id] === step.correctAnswer ? (
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5" />
                    <span>Correct! {step.explanation}</span>
                  </div>
                ) : (
                  <div>
                    <p>Incorrect. The correct answer is: {step.correctAnswer}</p>
                    {step.explanation && <p className="mt-1">{step.explanation}</p>}
                  </div>
                )}
              </div>
            )}
            
            {canAccess && !isCompleted && (
              <button
                onClick={() => handleStepComplete(stepIndex)}
                disabled={!userInputs[step.id]}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  userInputs[step.id]
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Submit Answer
              </button>
            )}
          </div>
        );

      default:
        return <div>Unsupported step type</div>;
    }
  };

  if (isCompleted) {
    return (
      <div className="p-6 text-center">
        <div className="flex items-center justify-center space-x-2 text-green-600 mb-4">
          <Award className="w-8 h-8" />
          <span className="text-xl font-medium">Interactive Activity Completed!</span>
        </div>
        <p className="text-gray-600 mb-6">
          Congratulations! You've successfully completed all steps of this interactive lesson.
        </p>
        <button
          onClick={resetActivity}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mx-auto"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Restart Activity</span>
        </button>
      </div>
    );
  }

  if (!hasStarted) {
    return (
      <div className="p-6 text-center space-y-6">
        <div className="flex items-center justify-center space-x-2 text-blue-600 mb-4">
          <Target className="w-8 h-8" />
          <span className="text-xl font-semibold">Interactive Learning Activity</span>
        </div>
        
        <div className="prose prose-lg max-w-none">
          <div dangerouslySetInnerHTML={{ __html: content.description }} />
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Activity Overview:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• {content.steps.length} interactive steps</li>
            <li>• Estimated time: {content.estimatedTime} minutes</li>
            <li>• Complete each step to progress</li>
            <li>• Hints available if you get stuck</li>
          </ul>
        </div>
        
        <button
          onClick={handleStart}
          className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
        >
          Start Activity
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Step {currentStep + 1} of {content.steps.length}
          </span>
          <span className="text-sm text-gray-600">
            {completedSteps.size}/{content.steps.length} completed
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(completedSteps.size / content.steps.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Steps Navigation */}
      <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
        {content.steps.map((_, index) => {
          const isCompleted = completedSteps.has(index);
          const isCurrent = index === currentStep;
          const canAccess = index === 0 || completedSteps.has(index - 1);
          
          return (
            <button
              key={index}
              onClick={() => canAccess && setCurrentStep(index)}
              disabled={!canAccess}
              className={`flex-shrink-0 w-10 h-10 rounded-full text-sm font-medium transition-colors ${
                isCompleted
                  ? 'bg-green-100 text-green-600 border-2 border-green-300'
                  : isCurrent
                  ? 'bg-blue-600 text-white'
                  : canAccess
                  ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              {isCompleted ? '✓' : index + 1}
            </button>
          );
        })}
      </div>

      {/* Current Step */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <div className="flex items-start justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            {content.steps[currentStep]?.title || `Step ${currentStep + 1}`}
          </h3>
          
          {content.steps[currentStep]?.hint && (
            <button
              onClick={() => toggleHint(currentStep)}
              className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm"
            >
              <Lightbulb className="w-4 h-4" />
              <span>{showHints[currentStep] ? 'Hide Hint' : 'Show Hint'}</span>
            </button>
          )}
        </div>

        {showHints[currentStep] && content.steps[currentStep]?.hint && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
            <div className="flex items-start space-x-2">
              <Lightbulb className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                {content.steps[currentStep].hint}
              </div>
            </div>
          </div>
        )}

        {renderStepContent(content.steps[currentStep], currentStep)}
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
          disabled={currentStep === 0}
          className={`px-4 py-2 rounded-md transition-colors ${
            currentStep === 0
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Previous
        </button>

        <button
          onClick={resetActivity}
          className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          <span>Restart</span>
        </button>

        <button
          onClick={() => setCurrentStep(Math.min(content.steps.length - 1, currentStep + 1))}
          disabled={currentStep === content.steps.length - 1 || !completedSteps.has(currentStep)}
          className={`px-4 py-2 rounded-md transition-colors ${
            currentStep === content.steps.length - 1 || !completedSteps.has(currentStep)
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default InteractiveContent;
