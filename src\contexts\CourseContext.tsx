import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Course, CourseState, CourseEnrollment, UserProgress, CourseFilters } from '../types/index';
import { useAuth } from './AuthContext';

interface CourseContextType {
  state: CourseState;
  loadCourses: (filters?: CourseFilters) => Promise<void>;
  loadCourse: (courseId: string) => Promise<void>;
  enrollInCourse: (courseId: string) => Promise<void>;
  unenrollFromCourse: (courseId: string) => Promise<void>;
  updateProgress: (progress: Partial<UserProgress>) => void;
  createCourse: (course: Omit<Course, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateCourse: (courseId: string, updates: Partial<Course>) => Promise<void>;
  deleteCourse: (courseId: string) => Promise<void>;
}

const CourseContext = createContext<CourseContextType | undefined>(undefined);

type CourseAction =
  | { type: 'LOAD_COURSES_START' }
  | { type: 'LOAD_COURSES_SUCCESS'; payload: Course[] }
  | { type: 'LOAD_COURSES_FAILURE'; payload: string }
  | { type: 'LOAD_COURSE_SUCCESS'; payload: Course }
  | { type: 'SET_ACTIVE_COURSE'; payload: Course | null }
  | { type: 'UPDATE_ENROLLMENTS'; payload: CourseEnrollment[] }
  | { type: 'ADD_ENROLLMENT'; payload: CourseEnrollment }
  | { type: 'REMOVE_ENROLLMENT'; payload: string }
  | { type: 'ADD_COURSE'; payload: Course }
  | { type: 'UPDATE_COURSE'; payload: { courseId: string; updates: Partial<Course> } }
  | { type: 'DELETE_COURSE'; payload: string };

const courseReducer = (state: CourseState, action: CourseAction): CourseState => {
  switch (action.type) {
    case 'LOAD_COURSES_START':
      return { ...state, isLoading: true, error: null };
    case 'LOAD_COURSES_SUCCESS':
      return { ...state, isLoading: false, courses: action.payload, error: null };
    case 'LOAD_COURSES_FAILURE':
      return { ...state, isLoading: false, error: action.payload };
    case 'LOAD_COURSE_SUCCESS':
      return {
        ...state,
        courses: state.courses.map(course =>
          course.id === action.payload.id ? action.payload : course
        ),
      };
    case 'SET_ACTIVE_COURSE':
      return { ...state, activeCourse: action.payload };
    case 'UPDATE_ENROLLMENTS':
      return { ...state, enrollments: action.payload };
    case 'ADD_ENROLLMENT':
      return { ...state, enrollments: [...state.enrollments, action.payload] };
    case 'REMOVE_ENROLLMENT':
      return {
        ...state,
        enrollments: state.enrollments.filter(e => e.courseId !== action.payload),
      };
    case 'ADD_COURSE':
      return { ...state, courses: [...state.courses, action.payload] };
    case 'UPDATE_COURSE':
      return {
        ...state,
        courses: state.courses.map(course =>
          course.id === action.payload.courseId
            ? { ...course, ...action.payload.updates }
            : course
        ),
        activeCourse:
          state.activeCourse?.id === action.payload.courseId
            ? { ...state.activeCourse, ...action.payload.updates }
            : state.activeCourse,
      };
    case 'DELETE_COURSE':
      return {
        ...state,
        courses: state.courses.filter(course => course.id !== action.payload),
        activeCourse: state.activeCourse?.id === action.payload ? null : state.activeCourse,
      };
    default:
      return state;
  }
};

const initialState: CourseState = {
  courses: [],
  activeCourse: null,
  enrollments: [],
  isLoading: false,
  error: null,
};

export const CourseProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(courseReducer, initialState);
  const { state: authState } = useAuth();

  // Mock data for demonstration
  const mockCourses: Course[] = [
    {
      id: '1',
      title: 'Introduction to React',
      description: 'Learn the fundamentals of React development',
      thumbnail: 'https://via.placeholder.com/300x200?text=React+Course',
      instructorId: 'instructor1',
      instructor: {
        id: 'instructor1',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'instructor' as any,
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
      },
      modules: [],
      tags: ['React', 'JavaScript', 'Frontend'],
      difficulty: 'beginner' as any,
      estimatedDuration: 480, // 8 hours
      isPublished: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      prerequisites: [],
    },
    {
      id: '2',
      title: 'Advanced TypeScript',
      description: 'Master advanced TypeScript concepts and patterns',
      thumbnail: 'https://via.placeholder.com/300x200?text=TypeScript+Course',
      instructorId: 'instructor2',
      instructor: {
        id: 'instructor2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'instructor' as any,
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=jane',
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
      },
      modules: [],
      tags: ['TypeScript', 'JavaScript', 'Advanced'],
      difficulty: 'advanced' as any,
      estimatedDuration: 720, // 12 hours
      isPublished: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      prerequisites: ['1'],
    },
  ];

  useEffect(() => {
    if (authState.user) {
      loadCourses();
    }
  }, [authState.user]);

  const loadCourses = async (filters?: CourseFilters): Promise<void> => {
    dispatch({ type: 'LOAD_COURSES_START' });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      let filteredCourses = [...mockCourses];
      
      if (filters) {
        if (filters.difficulty) {
          filteredCourses = filteredCourses.filter(course => course.difficulty === filters.difficulty);
        }
        if (filters.tags && filters.tags.length > 0) {
          filteredCourses = filteredCourses.filter(course =>
            course.tags.some(tag => filters.tags!.includes(tag))
          );
        }
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filteredCourses = filteredCourses.filter(course =>
            course.title.toLowerCase().includes(searchLower) ||
            course.description.toLowerCase().includes(searchLower)
          );
        }
      }
      
      dispatch({ type: 'LOAD_COURSES_SUCCESS', payload: filteredCourses });
    } catch (error) {
      dispatch({ type: 'LOAD_COURSES_FAILURE', payload: 'Failed to load courses' });
    }
  };

  const loadCourse = async (courseId: string): Promise<void> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const course = mockCourses.find(c => c.id === courseId);
      if (course) {
        dispatch({ type: 'LOAD_COURSE_SUCCESS', payload: course });
        dispatch({ type: 'SET_ACTIVE_COURSE', payload: course });
      }
    } catch (error) {
      dispatch({ type: 'LOAD_COURSES_FAILURE', payload: 'Failed to load course' });
    }
  };

  const enrollInCourse = async (courseId: string): Promise<void> => {
    if (!authState.user) return;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const enrollment: CourseEnrollment = {
        id: `enrollment-${Date.now()}`,
        userId: authState.user.id,
        courseId,
        enrolledAt: new Date(),
        progress: 0,
        status: 'active' as any,
      };
      
      dispatch({ type: 'ADD_ENROLLMENT', payload: enrollment });
    } catch (error) {
      dispatch({ type: 'LOAD_COURSES_FAILURE', payload: 'Failed to enroll in course' });
    }
  };

  const unenrollFromCourse = async (courseId: string): Promise<void> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      dispatch({ type: 'REMOVE_ENROLLMENT', payload: courseId });
    } catch (error) {
      dispatch({ type: 'LOAD_COURSES_FAILURE', payload: 'Failed to unenroll from course' });
    }
  };

  const updateProgress = (progress: Partial<UserProgress>): void => {
    // This would typically update progress in the backend
    // For now, we'll just log it
    console.log('Progress updated:', progress);
  };

  const createCourse = async (courseData: Omit<Course, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newCourse: Course = {
        ...courseData,
        id: `course-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      dispatch({ type: 'ADD_COURSE', payload: newCourse });
    } catch (error) {
      dispatch({ type: 'LOAD_COURSES_FAILURE', payload: 'Failed to create course' });
    }
  };

  const updateCourse = async (courseId: string, updates: Partial<Course>): Promise<void> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      dispatch({ type: 'UPDATE_COURSE', payload: { courseId, updates } });
    } catch (error) {
      dispatch({ type: 'LOAD_COURSES_FAILURE', payload: 'Failed to update course' });
    }
  };

  const deleteCourse = async (courseId: string): Promise<void> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      dispatch({ type: 'DELETE_COURSE', payload: courseId });
    } catch (error) {
      dispatch({ type: 'LOAD_COURSES_FAILURE', payload: 'Failed to delete course' });
    }
  };

  return (
    <CourseContext.Provider value={{
      state,
      loadCourses,
      loadCourse,
      enrollInCourse,
      unenrollFromCourse,
      updateProgress,
      createCourse,
      updateCourse,
      deleteCourse,
    }}>
      {children}
    </CourseContext.Provider>
  );
};

export const useCourse = (): CourseContextType => {
  const context = useContext(CourseContext);
  if (!context) {
    throw new Error('useCourse must be used within a CourseProvider');
  }
  return context;
};
